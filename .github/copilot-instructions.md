# GitHub Copilot Instructions for Shred Forwarder

## Project Overview

**Shred Forwarder** is a production-ready, high-performance Rust application that serves as a proxy server for Solana blockchain transaction streaming. The application connects to ERPC's Direct Shreds service, processes shreds into VersionedTransactions, and streams them to clients via gRPC with sub-millisecond latency.

**Current Status:** 🎉 PRODUCTION READY - Optimized Broadcast Architecture

**Key Architecture (Broadcast Model):**

```
Config.toml → Pre-configured Accounts → ERPC (account_include filter) → ALL Transactions → ALL gRPC Clients
                     ↑                                    ↑                              ↑
              Security Control                    Transaction Filtering           Broadcast All
```

**Performance Targets:**

-   Sub-millisecond transaction streaming latency
-   Support for thousands of concurrent gRPC connections
-   Memory-optimized with object pooling and atomic metrics
-   CPU affinity support for critical threads
-   Zero-allocation hot paths where possible

## Project Structure Guidelines

### Directory Structure (100% Implemented & Optimized)

```
shred-forwarder/
├── proto/shred_forwarder.proto    # ✅ gRPC Protocol Buffers definition
├── config.toml                    # ✅ Production-ready configuration with performance tuning
├── build.rs                       # ✅ Protocol Buffers build script
├── clippy.toml                    # ✅ Strict linting configuration
├── Makefile                       # ✅ Build automation and cross-platform support
└── src/
    ├── main.rs                    # ✅ Application entry point with performance optimizations
    ├── config/                    # ✅ Modular configuration management
    │   ├── mod.rs                 # ✅ Main config loader with validation
    │   ├── app.rs                 # ✅ App settings with pre-configured accounts
    │   ├── erpc.rs                # ✅ ERPC client configuration
    │   ├── grpc.rs                # ✅ gRPC server configuration with performance tuning
    │   ├── logging.rs             # ✅ Logging configuration
    │   └── performance.rs         # ✅ Performance optimization settings
    ├── client/                    # ✅ ERPC client implementation
    │   ├── mod.rs                 # ✅ Client exports
    │   └── erpc.rs                # ✅ ERPC connection with fixed subscription & reconnection
    ├── grpc/                      # ✅ gRPC server implementation
    │   ├── mod.rs                 # ✅ Module exports with generated proto
    │   ├── server.rs              # ✅ gRPC server with performance optimizations
    │   ├── service.rs             # ✅ Service implementation with validation
    │   └── broadcaster.rs         # ✅ Lock-free transaction broadcasting
    ├── types/                     # ✅ Type definitions and error handling
    │   ├── mod.rs                 # ✅ Type exports
    │   ├── error.rs               # ✅ Comprehensive error types
    │   └── result.rs              # ✅ Result type aliases
    └── utils/                     # ✅ Performance-optimized utilities
        ├── mod.rs                 # ✅ Utility exports
        ├── logging.rs             # ✅ Structured logging setup
        ├── transaction.rs         # ✅ Transaction conversion utilities
        ├── metrics.rs             # ✅ Atomic performance metrics
        ├── memory_pool.rs         # ✅ Memory pool implementation
        └── cpu_affinity.rs        # ✅ CPU affinity management
```

### Module Responsibilities (Fully Implemented & Optimized)

-   **✅ `proto/`**: Complete gRPC Protocol Buffer definitions with service interface
-   **✅ `config/`**: Modular configuration management with performance tuning options
-   **✅ `client/`**: ERPC connection with fixed subscription model and auto-reconnection
-   **✅ `grpc/`**: Full gRPC server implementation with account validation and broadcasting
-   **✅ `types/`**: Comprehensive error handling and shared types across all modules
-   **✅ `utils/`**: Performance-optimized utilities including metrics, memory pools, and CPU affinity

### Module Guidelines

-   Use shared types from `types/` module across all modules
-   Load configuration from `config/` module in all services
-   Leverage `utils/` for common functionality (retry, validation, metrics, performance)
-   Maintain clean dependencies, avoid circular dependencies
-   Each module should have a clear, single responsibility
-   Prioritize performance in all implementations

## Rust Guidelines

### Code Style and Formatting

-   Use `cargo fmt` for automatic code formatting
-   Follow Rust 2024 edition idioms and best practices
-   Use `snake_case` for variable and function names
-   Use `PascalCase` for types, structs, and enums
-   Use `SCREAMING_SNAKE_CASE` for constants
-   Prefer explicit return types for public functions
-   Use 4 spaces for indentation (configured in rustfmt.toml)
-   **No comments in code**: Keep code clean and self-explanatory without comments

### Error Handling

-   Use `anyhow::Result<T>` for functions that can fail
-   Use `thiserror` for custom error types when domain-specific errors are needed
-   Prefer `?` operator for error propagation
-   Add context to errors using `.context()` from anyhow
-   Never use `unwrap()` or `expect()` in production code - use proper error handling
-   Handle all error cases explicitly for robustness

### Async Programming

-   Use `tokio` runtime for all async operations
-   Prefer `async/await` over manual Future implementations
-   Use `tokio::spawn` for concurrent tasks
-   Use channels for communication between async tasks
-   Handle cancellation gracefully with `tokio::select!` when needed
-   Optimize async operations for minimal latency

### Memory Management

-   Prefer `&str` over `String` for function parameters when possible
-   Use `Arc<T>` for shared ownership in concurrent contexts
-   Use atomic operations instead of `Mutex<T>` for simple counters
-   Avoid unnecessary allocations in hot paths
-   Use `Vec<T>` for dynamic arrays, prefer iterators over collecting
-   Implement memory pools for frequently allocated objects
-   Use jemalloc as global allocator for better performance

## Project-Specific Guidelines

### Solana Integration

-   Use official Solana SDK crates: `solana-entry`, `solana-transaction`, etc.
-   Always use the latest stable versions of Solana dependencies
-   Handle `VersionedTransaction` properly for both Legacy and V0 formats
-   Implement proper account filtering based on pre-configured accounts
-   Optimize transaction processing for minimal latency

### gRPC Implementation

-   Use `tonic` framework for gRPC server and client implementations
-   Define clear protobuf schemas in `.proto` files with full VersionedTransaction structure
-   Handle streaming responses appropriately with proper error handling
-   Implement graceful shutdown for streaming connections
-   **Performance-first**: Use plain HTTP/2 without TLS for maximum speed in trusted environments
-   **Connection pooling**: Maintain persistent gRPC connections to avoid handshake overhead
-   **Binary serialization**: Optimize protobuf messages for minimal size and fast serialization
-   **TCP optimizations**: Enable TCP_NODELAY and configure keepalive settings

### ERPC Direct Shreds Integration

-   Use `solana-stream-sdk` for connecting to ERPC services
-   Implement automatic reconnection logic with exponential backoff
-   Handle shred deduplication efficiently
-   Subscribe to pre-configured accounts using account_include filter
-   Process shreds with minimal latency and maximum throughput

### Configuration Management

-   Use `config` crate for loading configuration from files and environment
-   Support TOML configuration files with smart defaults
-   Validate configuration on startup with comprehensive error messages
-   Include performance optimization settings in configuration
-   Minimal required configuration: ERPC endpoint and accounts list

### Logging and Monitoring

-   Use `tracing` and `tracing-subscriber` for structured logging
-   Include relevant context in log messages (slot numbers, account addresses, etc.)
-   Use appropriate log levels: `error!`, `warn!`, `info!`, `debug!`, `trace!`
-   **Performance metrics at info level**: Log performance and metrics as info instead of debug
-   Implement atomic metrics for lock-free performance tracking
-   Configurable metrics logging intervals

## Architecture Patterns

### Connection Management

-   Implement connection pooling for ERPC connections
-   Use health checks to monitor connection status
-   Handle network failures gracefully with retry logic
-   Maintain connection state with proper cleanup
-   Optimize for persistent connections to minimize handshake overhead

### Data Processing Pipeline

```
Raw Shreds → Shred Decoding → Entry Extraction → Transaction Broadcasting → gRPC Streaming
```

### Performance Optimization

-   Use efficient data structures for account filtering (HashSet)
-   Minimize allocations in the hot path
-   Use zero-copy operations where possible
-   Implement proper buffering for streaming data
-   Memory pools for frequently allocated objects
-   Atomic metrics for lock-free performance tracking

### Realtime Streaming Optimization

-   **Avoid TLS overhead**: Use plain HTTP/2 for gRPC connections to minimize encryption overhead
-   **TCP_NODELAY**: Enable TCP_NODELAY on all socket connections to reduce latency
-   **Connection reuse**: Maintain persistent connections, avoid frequent reconnections
-   **Minimal serialization**: Use binary formats, avoid JSON/text serialization in hot paths
-   **Lock-free data structures**: Prefer atomic operations and lock-free algorithms where possible
-   **CPU affinity**: Pin critical threads to specific CPU cores to avoid context switching
-   **Memory pools**: Pre-allocate buffers and reuse memory to avoid garbage collection pauses
-   **Batch processing**: Process multiple shreds/transactions in batches when possible
-   **Inline small functions**: Use `#[inline]` attribute for frequently called small functions
-   **Compile optimizations**: Use `lto = true` and `codegen-units = 1` in release profile
-   **jemalloc allocator**: Use jemalloc for better multi-threaded memory allocation performance

## Security Considerations

### Credential Management

-   Never hardcode API keys or credentials in source code
-   Use environment variables or secure configuration files
-   Validate all input from external sources (shreds, gRPC requests)
-   Implement proper authentication for gRPC endpoints if required
-   Pre-configured account lists for security control

### Network Security

-   Validate and sanitize account addresses
-   Implement rate limiting to prevent abuse
-   Monitor for unusual patterns in incoming data
-   **Performance vs Security**: Prioritize speed over TLS encryption in trusted network environments
-   **Input validation**: Use fast validation methods that don't compromise latency
-   Account validation against pre-configured lists

## Dependencies and Versions

### Core Dependencies (Latest Stable)

-   `tokio` - Async runtime for high-performance async operations
-   `anyhow` - Error handling with context
-   `thiserror` - Custom error types
-   `config` - Configuration management from TOML files
-   `tracing` - Structured logging
-   `tonic` - gRPC framework for high-performance streaming
-   `prost` - Protocol Buffers implementation
-   `solana-stream-sdk` - ERPC integration for Solana shreds
-   `solana-entry` - Solana blockchain primitives
-   `solana-transaction` - Transaction handling
-   `serde` - Serialization/deserialization
-   `bincode` - Binary serialization
-   `jemallocator` - High-performance memory allocator
-   `core_affinity` - CPU affinity management

### Development Dependencies

-   `tonic-build` - Protocol Buffers build support
-   `clippy` - Linting (strict configuration in clippy.toml)
-   `rustfmt` - Code formatting

## Development Policies

### Testing and Documentation

-   **No testing required**: This application does not require unit tests, integration tests, or any testing infrastructure
-   **No documentation required**: Do not generate rustdoc comments, README updates, or any form of documentation
-   **No comments in code**: Do not add any comments in source code, keep code clean and self-explanatory
-   **Focused implementation**: Create only exactly what is requested, avoid adding extra features or utilities

### Code Modification Guidelines

-   **Preserve existing logic**: When editing code, minimize changes to existing logic and functionality
-   **Confirmation required**: Any modifications that could alter existing behavior must be confirmed before implementation
-   **Surgical changes**: Make precise, targeted modifications rather than broad refactoring
-   **Performance first**: Always prioritize performance optimizations in modifications

## Code Quality Standards

### Clippy Configuration

-   Follow the strict clippy rules defined in clippy.toml
-   Cognitive complexity threshold: 30
-   Maximum function lines: 100
-   Handle all clippy warnings as errors in CI
-   Zero warnings policy for production code

### Performance Requirements

-   Target sub-millisecond latency for transaction streaming
-   Minimize memory allocation in critical paths
-   Handle thousands of concurrent gRPC connections
-   Efficient shred processing with minimal CPU usage
-   Memory pools for object reuse
-   Atomic metrics for lock-free performance tracking

## Performance Optimization Guidelines

### Memory Management Optimizations

-   **Memory Pools**: Use pre-allocated object pools for frequently created/destroyed objects
    -   Buffer pools for serialization/deserialization operations
    -   Configure pool sizes based on expected throughput
    -   Monitor pool hit rates and utilization
-   **Atomic Metrics**: Replace Mutex-based counters with atomic operations for lock-free performance tracking
-   **Custom Allocators**: Use jemalloc as global allocator for better multi-threaded performance
-   **Zero-copy operations**: Minimize data copying in hot paths

### CPU Affinity and Threading

-   **CPU Affinity**: Pin critical threads to specific CPU cores to avoid context switching overhead
    -   ERPC client thread on dedicated core
    -   gRPC server threads on separate cores
    -   Configurable via `performance.cpu_affinity` settings
-   **Thread Isolation**: Separate I/O-bound and CPU-bound operations across different threads
-   **Lock-free algorithms**: Use atomic operations instead of mutexes where possible

### Configuration Best Practices

-   **Performance Config Section**: Use `[performance]` section in config.toml for optimization settings
-   **Memory Pool Configuration**: Tune initial and maximum pool sizes based on workload
-   **Metrics Configuration**: Enable/disable performance metrics and logging intervals
-   **CPU Affinity Settings**: Configure core assignments for optimal performance
-   **Smart Defaults**: All performance settings optimized for production use

### Atomic Metrics Implementation

-   Use atomic operations for performance counters
-   Lock-free metrics collection for minimal overhead
-   Configurable metrics logging intervals
-   Performance metrics logged at info level instead of debug

### Performance Monitoring

-   **Automatic Metrics Logging**: Configurable interval-based performance summaries
-   **Pool Statistics**: Monitor memory pool hit rates and utilization
-   **Processing Timers**: Track end-to-end transaction processing latency
-   **Connection Metrics**: Monitor gRPC connection counts and ERPC reconnections
-   **Throughput Metrics**: Track transactions per second and processing times

### Code Quality Standards

-   **Zero Warnings Policy**: All code must compile without warnings
-   **Clippy Compliance**: Must pass `cargo clippy -- -D warnings`
-   **Clean Architecture**: Remove unused code, imports, and dependencies
-   **Performance First**: All optimizations enabled by default with smart defaults
-   **Dependency Management**: Keep dependencies minimal and up-to-date

### Configuration Philosophy

-   **Minimal Required Config**: Only ERPC endpoint and accounts list required
-   **Smart Defaults**: All performance settings optimized for production
-   **Backward Compatibility**: Existing configurations continue to work
-   **Performance Tuning**: Configurable TCP settings, memory pools, and CPU affinity

## Build and Deployment

### Cross-Platform Support

-   Support macOS (ARM64) and Linux (x86_64) targets
-   Use the provided Makefile for consistent builds
-   Create release binaries for distribution
-   Rust 2024 edition for latest language features

### Optimization Flags

-   **LTO**: Link Time Optimization enabled for release builds
-   **Single Codegen Unit**: Better optimization at cost of compile time
-   **Panic Abort**: Smaller binary size for production
-   **Symbol Stripping**: Remove debug symbols from release builds
-   **Development Optimization**: opt-level = 1 for faster debug builds

### Dependency Management

-   **Keep dependencies minimal**: Only include necessary crates
-   **Regular updates**: Use `cargo update` to keep dependencies current
-   **Remove unused dependencies**: Use `cargo machete` to identify unused crates
-   **Version pinning**: Use specific versions for reproducible builds

## Current Project Status

### Implemented Features ✅

-   **Core Architecture**: Complete broadcast model implementation
-   **ERPC Integration**: Direct shreds connection with account filtering
-   **gRPC Server**: High-performance streaming with full VersionedTransaction support
-   **Configuration Management**: TOML-based config with performance tuning
-   **Performance Optimizations**: Memory pools, atomic metrics, CPU affinity
-   **Error Handling**: Comprehensive error types with proper context
-   **Logging**: Structured logging with configurable levels
-   **Build System**: Cross-platform Makefile with optimization flags

### Performance Optimizations ✅

-   **Memory Pools**: Buffer pools for serialization operations
-   **Atomic Metrics**: Lock-free performance tracking
-   **CPU Affinity**: Configurable thread pinning
-   **jemalloc**: High-performance memory allocator
-   **TCP Optimizations**: Configurable TCP_NODELAY and keepalive settings
-   **Zero Warnings**: Clean codebase with no compiler warnings

### Configuration Features ✅

-   **Smart Defaults**: Minimal configuration required
-   **Performance Tuning**: Configurable memory pools, metrics, and CPU affinity
-   **Account Security**: Pre-configured account lists for access control
-   **Logging Control**: Configurable log levels and formats

This project is production-ready with comprehensive performance optimizations and follows all Rust best practices for high-performance applications.
