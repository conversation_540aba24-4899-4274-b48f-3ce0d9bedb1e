# GitHub Copilot Instructions for Shred Forwarder

## Project Overview

This is a production-ready, high-performance Rust CLI application that serves as a proxy server for Solana blockchain transaction streaming. The application connects to ERPC's Direct Shreds service, processes shreds into VersionedTransactions, and streams them to clients via gRPC with sub-millisecond latency.

**Current Status:** 🎉 PRODUCTION READY - Broadcast Architecture Implemented

**Key Architecture (Broadcast Model):**

```
Config.toml → Pre-configured Accounts → ERPC (account_include filter) → ALL Transactions → ALL gRPC Clients
                     ↑                                    ↑                              ↑
              Security Control                    Transaction Filtering           Broadcast All
```

## Project Structure Guidelines

### Directory Structure (100% Implemented)

```
shred-forwarder/
├── proto/shred_forwarder.proto    # ✅ gRPC Protocol Buffers definition
├── config.toml                    # ✅ Production-ready configuration
├── build.rs                       # ✅ Protocol Buffers build script
└── src/
    ├── main.rs                    # ✅ Application entry point với integration
    ├── config/                    # ✅ Modular configuration management
    │   ├── mod.rs                 # ✅ Main config loader
    │   ├── app.rs                 # ✅ App settings với pre-configured accounts
    │   ├── erpc.rs                # ✅ ERPC client configuration
    │   ├── grpc.rs                # ✅ gRPC server configuration
    │   └── logging.rs             # ✅ Logging configuration
    ├── client/                    # ✅ ERPC client implementation
    │   ├── mod.rs                 # ✅ Client exports
    │   └── erpc.rs                # ✅ ERPC connection với fixed subscription
    ├── grpc/                      # ✅ gRPC server implementation
    │   ├── mod.rs                 # ✅ Module exports
    │   ├── server.rs              # ✅ gRPC server
    │   ├── service.rs             # ✅ Service implementation với validation
    │   └── broadcaster.rs         # ✅ Transaction broadcasting
    ├── types/                     # ✅ Type definitions và error handling
    │   ├── mod.rs                 # ✅ Type exports
    │   ├── error.rs               # ✅ Comprehensive error types
    │   └── result.rs              # ✅ Result type aliases
    └── utils/                     # ✅ Utilities
        ├── mod.rs                 # ✅ Utility exports
        ├── logging.rs             # ✅ Structured logging setup
        └── transaction.rs         # ✅ Transaction conversion utilities
```

### Module Responsibilities (Implemented)

-   **✅ `proto/`**: Complete gRPC Protocol Buffer definitions với service interface
-   **✅ `config/`**: Modular configuration management với pre-configured accounts security
-   **✅ `client/`**: ERPC connection với fixed subscription model và auto-reconnection
-   **✅ `grpc/`**: Full gRPC server implementation với account validation và broadcasting
-   **✅ `types/`**: Comprehensive error handling và shared types across all modules
-   **✅ `utils/`**: Structured logging, transaction conversion utilities và helper functions

### Module Guidelines

-   Use shared types from `types/` module across all modules
-   Load configuration from `config/` module in all services
-   Leverage `utils/` for common functionality (retry, validation, metrics)
-   Maintain clean dependencies, avoid circular dependencies
-   Each module should have a clear, single responsibility

## Rust Guidelines

### Code Style and Formatting

-   Use `cargo fmt` for automatic code formatting
-   Follow Rust 2024 edition idioms and best practices
-   Use `snake_case` for variable and function names
-   Use `PascalCase` for types, structs, and enums
-   Use `SCREAMING_SNAKE_CASE` for constants
-   Prefer explicit return types for public functions
-   Use 4 spaces for indentation (configured in rustfmt.toml)

### Error Handling

-   Use `anyhow::Result<T>` for functions that can fail
-   Use `thiserror` for custom error types when domain-specific errors are needed
-   Prefer `?` operator for error propagation
-   Add context to errors using `.context()` from anyhow
-   Never use `unwrap()` or `expect()` in production code - use proper error handling

### Async Programming

-   Use `tokio` runtime for all async operations
-   Prefer `async/await` over manual Future implementations
-   Use `tokio::spawn` for concurrent tasks
-   Use channels for communication between async tasks
-   Handle cancellation gracefully with `tokio::select!` when needed

### Memory Management

-   Prefer `&str` over `String` for function parameters when possible
-   Use `Arc<T>` for shared ownership in concurrent contexts
-   Use `Mutex<T>` or `RwLock<T>` for shared mutable state
-   Avoid unnecessary allocations in hot paths
-   Use `Vec<T>` for dynamic arrays, prefer iterators over collecting

## Project-Specific Guidelines

### Solana Integration

-   Use official Solana SDK crates: `solana-entry`, `solana-transaction`, etc.
-   Always use the latest stable versions of Solana dependencies
-   Handle `VersionedTransaction` properly for both Legacy and V0 formats
-   Implement proper account filtering based on client subscriptions

### gRPC Implementation

-   Use `tonic` framework for gRPC server and client implementations
-   Define clear protobuf schemas in `.proto` files
-   Handle streaming responses appropriately with proper error handling
-   Implement graceful shutdown for streaming connections
-   **Performance-first**: Use plain HTTP/2 without TLS for maximum speed in trusted environments
-   **Connection pooling**: Maintain persistent gRPC connections to avoid handshake overhead
-   **Binary serialization**: Optimize protobuf messages for minimal size and fast serialization

### ERPC Direct Shreds Integration

-   Use `solana-stream-sdk` for connecting to ERPC services
-   Implement automatic reconnection logic with exponential backoff
-   Handle shred deduplication efficiently
-   Subscribe only to necessary accounts to optimize performance

### Configuration Management

-   Use `config` crate for loading configuration from files and environment
-   Support both TOML configuration files and environment variables
-   Validate configuration on startup

### Logging and Monitoring

-   Use `tracing` and `tracing-subscriber` for structured logging
-   Include relevant context in log messages (slot numbers, account addresses, etc.)
-   Use appropriate log levels: `error!`, `warn!`, `info!`, `debug!`, `trace!`
-   Log performance metrics for critical operations

## Architecture Patterns

### Connection Management

-   Implement connection pooling for ERPC connections
-   Use health checks to monitor connection status
-   Handle network failures gracefully with retry logic
-   Maintain connection state with proper cleanup

### Data Processing Pipeline

```
Raw Shreds → Shred Decoding → Entry Extraction → Transaction Filtering → gRPC Broadcasting
```

### Performance Optimization

-   Use efficient data structures for account filtering (HashSet, Bloom filters)
-   Minimize allocations in the hot path
-   Use zero-copy operations where possible
-   Implement proper buffering for streaming data

### Realtime Streaming Optimization

-   **Avoid TLS overhead**: Use plain HTTP/2 for gRPC connections to minimize encryption overhead
-   **TCP_NODELAY**: Enable TCP_NODELAY on all socket connections to reduce latency
-   **Connection reuse**: Maintain persistent connections, avoid frequent reconnections
-   **Minimal serialization**: Use binary formats, avoid JSON/text serialization in hot paths
-   **Lock-free data structures**: Prefer atomic operations and lock-free algorithms where possible
-   **CPU affinity**: Pin critical threads to specific CPU cores to avoid context switching
-   **Memory pools**: Pre-allocate buffers and reuse memory to avoid garbage collection pauses
-   **Batch processing**: Process multiple shreds/transactions in batches when possible
-   **Inline small functions**: Use `#[inline]` attribute for frequently called small functions
-   **Compile optimizations**: Use `lto = true` and `codegen-units = 1` in release profile

## Security Considerations

### Credential Management

-   Never hardcode API keys or credentials in source code
-   Use environment variables or secure configuration files
-   Validate all input from external sources (shreds, gRPC requests)
-   Implement proper authentication for gRPC endpoints if required

### Network Security

-   Validate and sanitize account addresses
-   Implement rate limiting to prevent abuse
-   Monitor for unusual patterns in incoming data
-   **Performance vs Security**: Prioritize speed over TLS encryption in trusted network environments
-   **Input validation**: Use fast validation methods that don't compromise latency

## Dependencies and Versions

### Core Dependencies

-   `tokio` - Async runtime (latest stable)
-   `anyhow` - Error handling
-   `config` - Configuration management
-   `tracing` - Logging
-   `tonic` - gRPC framework
-   `solana-stream-sdk` - ERPC integration
-   `solana-entry` - Solana blockchain primitives

### Development Dependencies

-   `cargo-make` - Build automation
-   `clippy` - Linting (strict configuration in clippy.toml)
-   `rustfmt` - Code formatting

## Development Policies

### Testing and Documentation

-   **No testing required**: This application does not require unit tests, integration tests, or any testing infrastructure
-   **No documentation required**: Do not generate rustdoc comments, README updates, or any form of documentation
-   **No comments in code**: Do not add any comments in source code, keep code clean and self-explanatory
-   **Focused implementation**: Create only exactly what is requested, avoid adding extra features or utilities

### Code Modification Guidelines

-   **Preserve existing logic**: When editing code, minimize changes to existing logic and functionality
-   **Confirmation required**: Any modifications that could alter existing behavior must be confirmed before implementation
-   **Surgical changes**: Make precise, targeted modifications rather than broad refactoring

## Code Quality Standards

### Clippy Configuration

-   Follow the strict clippy rules defined in clippy.toml
-   Cognitive complexity threshold: 30
-   Maximum function lines: 100
-   Handle all clippy warnings as errors in CI

### Performance Requirements

-   Target sub-millisecond latency for transaction streaming
-   Minimize memory allocation in critical paths
-   Handle thousands of concurrent gRPC connections
-   Efficient shred processing with minimal CPU usage

## Performance Optimization Guidelines

### Memory Management Optimizations

-   **Memory Pools**: Use pre-allocated object pools for frequently created/destroyed objects
    -   Transaction pools for `VersionedTransaction` objects
    -   Buffer pools for serialization/deserialization operations
    -   Configure pool sizes based on expected throughput
-   **Atomic Metrics**: Replace Mutex-based counters with atomic operations for lock-free performance tracking
-   **Custom Allocators**: Use jemalloc as global allocator for better multi-threaded performance

### CPU Affinity and Threading

-   **CPU Affinity**: Pin critical threads to specific CPU cores to avoid context switching overhead
    -   ERPC client thread on dedicated core
    -   gRPC server threads on separate cores
    -   Configurable via `performance.cpu_affinity` settings
-   **Thread Isolation**: Separate I/O-bound and CPU-bound operations across different threads

### Configuration Best Practices

-   **Performance Config Section**: Use `[performance]` section in config.toml for optimization settings
-   **Memory Pool Configuration**: Tune initial and maximum pool sizes based on workload
-   **Metrics Configuration**: Enable/disable performance metrics and logging intervals
-   **CPU Affinity Settings**: Configure core assignments for optimal performance

### Memory Pool Usage Patterns

```rust
// Use memory pools for frequent allocations
let pooled_transaction = get_pooled_transaction();
let transaction = pooled_transaction.take();
// ... use transaction
// Automatic return to pool on drop
```

### Atomic Metrics Implementation

```rust
// Use atomic operations for performance counters
get_metrics().increment_transactions();
get_metrics().add_processing_time(duration);
// Lock-free metrics collection
```

### Performance Monitoring

-   **Automatic Metrics Logging**: Configurable interval-based performance summaries
-   **Pool Statistics**: Monitor memory pool hit rates and utilization
-   **Processing Timers**: Track end-to-end transaction processing latency
-   **Connection Metrics**: Monitor gRPC connection counts and ERPC reconnections

### Code Quality Standards

-   **Zero Warnings Policy**: All code must compile without warnings
-   **Clippy Compliance**: Must pass `cargo clippy -- -D warnings`
-   **Clean Architecture**: Remove unused code, imports, and dependencies
-   **Performance First**: All optimizations enabled by default with smart defaults

### Configuration Philosophy

-   **Minimal Required Config**: Only ERPC endpoint and accounts list required
-   **Smart Defaults**: All performance settings optimized for production
-   **Backward Compatibility**: Existing configurations continue to work
-   **Clear Documentation**: Every config option documented with examples

### Memory Pool Usage

-   **Buffer Pools Only**: Transaction pools removed as unused infrastructure
-   **Active Integration**: Pools used in ERPC client for serialization operations
-   **Configurable Sizes**: Initial and maximum pool sizes tunable via config
-   **Statistics Monitoring**: Optional pool performance logging

## Build and Deployment

### Cross-Platform Support

-   Support macOS (ARM64) and Linux (x86_64) targets
-   Use the provided Makefile for consistent builds
-   Create release binaries for distribution

### Optimization Flags

-   **LTO**: Link Time Optimization enabled for release builds
-   **Single Codegen Unit**: Better optimization at cost of compile time
-   **Panic Abort**: Smaller binary size for production
-   **Symbol Stripping**: Remove debug symbols from release builds
