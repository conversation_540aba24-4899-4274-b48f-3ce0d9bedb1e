{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 12074263998246110377, "profile": 5676177281124120482, "path": 2695021009374120339, "deps": [[40386456601120721, "percent_encoding", false, 1693554498400272352], [264090853244900308, "sync_wrapper", false, 8449400829150247655], [784494742817713399, "tower_service", false, 13815338185625570980], [1906322745568073236, "pin_project_lite", false, 17572144356043061255], [3129130049864710036, "memchr", false, 15247155475556316303], [3601586811267292532, "tower", false, 9670281373180093618], [4405182208873388884, "http", false, 9705911327783299346], [7414427314941361239, "hyper", false, 15073222247708642448], [7695812897323945497, "itoa", false, 2478430321954232087], [7712452662827335977, "tower_layer", false, 17633485529973314294], [8915503303801890683, "http_body", false, 18285392524156361121], [9293824762099617471, "axum_core", false, 8377482506431442969], [9678799920983747518, "matchit", false, 2578084785127551243], [9689903380558560274, "serde", false, 17554119073881279777], [10229185211513642314, "mime", false, 889530669170861422], [10435729446543529114, "bitflags", false, 3706872807597275699], [10629569228670356391, "futures_util", false, 7894425601274856401], [11946729385090170470, "async_trait", false, 8527521342054434220], [16066129441945555748, "bytes", false, 376513669882553100], [16244562316228021087, "build_script_build", false, 4744151649324251529]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/axum-60b4663f75b65ea2/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}