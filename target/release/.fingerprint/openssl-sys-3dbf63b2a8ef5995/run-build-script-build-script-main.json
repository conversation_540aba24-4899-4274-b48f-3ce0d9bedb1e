{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[9070360545695802481, "build_script_main", false, 85023605015530382]], "local": [{"RerunIfChanged": {"output": "release/build/openssl-sys-3dbf63b2a8ef5995/output", "paths": ["build/expando.c"]}}, {"RerunIfEnvChanged": {"var": "AARCH64_APPLE_DARWIN_OPENSSL_NO_VENDOR", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_NO_VENDOR", "val": null}}, {"RerunIfEnvChanged": {"var": "AARCH64_APPLE_DARWIN_OPENSSL_CONFIG_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_CONFIG_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "MACOSX_DEPLOYMENT_TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "AR_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_AR", "val": null}}, {"RerunIfEnvChanged": {"var": "AR", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_ARFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "ARFLAGS_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "RANLIB_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "RANLIB_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_RANLIB", "val": null}}, {"RerunIfEnvChanged": {"var": "RANLIB", "val": null}}, {"RerunIfEnvChanged": {"var": "RANLIBFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_RANLIBFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "RANLIBFLAGS_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "RANLIBFLAGS_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "MACOSX_DEPLOYMENT_TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "AARCH64_APPLE_DARWIN_OPENSSL_LIBS", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_LIBS", "val": null}}, {"RerunIfEnvChanged": {"var": "AARCH64_APPLE_DARWIN_OPENSSL_STATIC", "val": null}}, {"RerunIfEnvChanged": {"var": "OPENSSL_STATIC", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}