{"rustc": 15497389221046826682, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 3882023571813807903, "profile": 17984201634715228204, "path": 11967964535478927124, "deps": [[99783594999256520, "prost_build", false, 18056813049552751368], [3060637413840920116, "proc_macro2", false, 1177530198798952237], [8549471757621926118, "prettyplease", false, 2240602790245276186], [17990358020177143287, "quote", false, 2695568740530275677], [18149961000318489080, "syn", false, 11506556419293329568]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tonic-build-4d4088d5d64b233e/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}