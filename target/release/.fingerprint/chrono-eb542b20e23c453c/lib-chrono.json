{"rustc": 15497389221046826682, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 5676177281124120482, "path": 16800623322018478868, "deps": [[5157631553186200874, "num_traits", false, 17606154156261875864], [7910860254152155345, "iana_time_zone", false, 13109498228011436790], [9689903380558560274, "serde", false, 17554119073881279777]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/chrono-eb542b20e23c453c/dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}