{"rustc": 15497389221046826682, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 3541797763817303166, "path": 17770270771451006161, "deps": [[1009387600818341822, "matchers", false, 7265178307847560525], [1017461770342116999, "sharded_slab", false, 5069715553402822667], [3722963349756955755, "once_cell", false, 17058549894504797386], [6048213226671835012, "smallvec", false, 17756372617609688465], [6981130804689348050, "tracing_serde", false, 6516089490126063520], [8606274917505247608, "tracing", false, 2266138977506242905], [8614575489689151157, "nu_ansi_term", false, 1847015077818974944], [9451456094439810778, "regex", false, 13571234963917598065], [9689903380558560274, "serde", false, 17554119073881279777], [10806489435541507125, "tracing_log", false, 8939273676298517468], [11033263105862272874, "tracing_core", false, 3681535588776390608], [12409575957772518135, "time", false, 11426303674621966590], [12427285511609802057, "thread_local", false, 10531088284243584174], [15367738274754116744, "serde_json", false, 16071303312748491755]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tracing-subscriber-16b5c42ce6f9319d/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}