{"rustc": 15497389221046826682, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 9025750215440372010, "profile": 7408076739112287915, "path": 7934114986830911633, "deps": [[2739579679802620019, "prost_build", false, 6229543230654764505], [3060637413840920116, "proc_macro2", false, 1177530198798952237], [8549471757621926118, "prettyplease", false, 2240602790245276186], [16470553738848018267, "prost_types", false, 11532191494303113446], [17990358020177143287, "quote", false, 2695568740530275677], [18149961000318489080, "syn", false, 11506556419293329568]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tonic-build-4c7e985689920c90/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}