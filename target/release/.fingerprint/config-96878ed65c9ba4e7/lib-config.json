{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 10937020577594238424, "path": 10162044332273838614, "deps": [[1213098572879462490, "json5_rs", false, 6164013726256385585], [1238778183371849706, "yaml_rust2", false, 8674075932853769289], [2244620803250265856, "ron", false, 17105566021835415358], [2356429411733741858, "ini", false, 664009987854988039], [6517602928339163454, "path<PERSON><PERSON>", false, 2359228693123708376], [8786711029710048183, "toml", false, 10256301728999096155], [9689903380558560274, "serde", false, 17554119073881279777], [11946729385090170470, "async_trait", false, 8527521342054434220], [13475460906694513802, "convert_case", false, 3692563404027707442], [14718834678227948963, "winnow", false, 3966999569685423991], [15367738274754116744, "serde_json", false, 16071303312748491755]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/config-96878ed65c9ba4e7/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}