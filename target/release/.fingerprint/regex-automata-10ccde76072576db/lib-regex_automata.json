{"rustc": 15497389221046826682, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode-case\", \"unicode-perl\", \"unicode-word-boundary\"]", "declared_features": "[\"alloc\", \"default\", \"dfa\", \"dfa-build\", \"dfa-onepass\", \"dfa-search\", \"hybrid\", \"internal-instrument\", \"internal-instrument-pikevm\", \"logging\", \"meta\", \"nfa\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 4726246767843925232, "profile": 5676177281124120482, "path": 2365493837025371978, "deps": [[2779309023524819297, "aho_corasick", false, 12713869473604338827], [3129130049864710036, "memchr", false, 15247155475556316303], [9408802513701742484, "regex_syntax", false, 8292532943465797991]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-automata-10ccde76072576db/dep-lib-regex_automata", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}