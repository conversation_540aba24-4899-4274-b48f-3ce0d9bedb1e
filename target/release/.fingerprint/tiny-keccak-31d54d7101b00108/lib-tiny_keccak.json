{"rustc": 15497389221046826682, "features": "[\"default\", \"shake\"]", "declared_features": "[\"cshake\", \"default\", \"fips202\", \"k12\", \"keccak\", \"kmac\", \"parallel_hash\", \"sha3\", \"shake\", \"sp800\", \"tuple_hash\"]", "target": 8989851571439621957, "profile": 17984201634715228204, "path": 8572917723393887027, "deps": [[4280712380738690914, "build_script_build", false, 5390446902248264247], [10633447223404403777, "crunchy", false, 6564073108853296454]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tiny-keccak-31d54d7101b00108/dep-lib-tiny_keccak", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}