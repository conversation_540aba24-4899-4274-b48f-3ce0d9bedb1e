{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private_docs\", \"tracing\"]", "target": 2565713999752801252, "profile": 5449015932126939506, "path": 12675119491120528943, "deps": [[784494742817713399, "tower_service", false, 13815338185625570980], [1906322745568073236, "pin_project_lite", false, 17572144356043061255], [2517136641825875337, "sync_wrapper", false, 16051944080121878777], [7620660491849607393, "futures_core", false, 2091735580039771142], [7712452662827335977, "tower_layer", false, 17633485529973314294], [7858942147296547339, "rustversion", false, 13787539941178566004], [9010263965687315507, "http", false, 9985776661874788213], [10229185211513642314, "mime", false, 889530669170861422], [14084095096285906100, "http_body", false, 3650531633109126669], [16066129441945555748, "bytes", false, 376513669882553100], [16900715236047033623, "http_body_util", false, 4434650290111585496]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/axum-core-6b1d52abff4ece13/dep-lib-axum_core", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}