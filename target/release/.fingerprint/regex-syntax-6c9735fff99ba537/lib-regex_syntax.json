{"rustc": 15497389221046826682, "features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 7529137146482485884, "profile": 5676177281124120482, "path": 4354749700453370759, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-syntax-6c9735fff99ba537/dep-lib-regex_syntax", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}