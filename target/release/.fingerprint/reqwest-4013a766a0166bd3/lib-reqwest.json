{"rustc": 15497389221046826682, "features": "[\"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"deflate\", \"gzip\", \"hyper-rustls\", \"json\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-rustls\", \"tokio-util\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 5676177281124120482, "path": 12360358679216226155, "deps": [[40386456601120721, "percent_encoding", false, 1693554498400272352], [95042085696191081, "ipnet", false, 2103215548243270197], [126872836426101300, "async_compression", false, 851520086434042887], [264090853244900308, "sync_wrapper", false, 8449400829150247655], [784494742817713399, "tower_service", false, 13815338185625570980], [1044435446100926395, "hyper_rustls", false, 5695188703479418801], [1288403060204016458, "tokio_util", false, 13403914671272393266], [1906322745568073236, "pin_project_lite", false, 17572144356043061255], [3150220818285335163, "url", false, 405269121369660967], [3722963349756955755, "once_cell", false, 17058549894504797386], [4405182208873388884, "http", false, 9705911327783299346], [5986029879202738730, "log", false, 4833805747564492888], [7414427314941361239, "hyper", false, 15073222247708642448], [7620660491849607393, "futures_core", false, 2091735580039771142], [8915503303801890683, "http_body", false, 18285392524156361121], [9538054652646069845, "tokio", false, 10509612637553025725], [9689903380558560274, "serde", false, 17554119073881279777], [10229185211513642314, "mime", false, 889530669170861422], [10629569228670356391, "futures_util", false, 7894425601274856401], [11107720164717273507, "system_configuration", false, 3735446186254520850], [11295624341523567602, "rustls", false, 3015728665422461230], [13809605890706463735, "h2", false, 1741547492636331080], [14564311161534545801, "encoding_rs", false, 125140991963461980], [15367738274754116744, "serde_json", false, 16071303312748491755], [16066129441945555748, "bytes", false, 376513669882553100], [16311359161338405624, "rustls_pemfile", false, 11590656996849463391], [16542808166767769916, "serde_urlencoded", false, 16965424212367022736], [16622232390123975175, "tokio_rustls", false, 1934617558529544750], [17652733826348741533, "webpki_roots", false, 9656076743261325984], [18066890886671768183, "base64", false, 14289460333925399441]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/reqwest-4013a766a0166bd3/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}