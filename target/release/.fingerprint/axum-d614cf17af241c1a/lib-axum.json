{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"__private\", \"__private_docs\", \"default\", \"form\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 13920321295547257648, "profile": 5449015932126939506, "path": 17301405828534346599, "deps": [[40386456601120721, "percent_encoding", false, 1693554498400272352], [784494742817713399, "tower_service", false, 13815338185625570980], [1906322745568073236, "pin_project_lite", false, 17572144356043061255], [2517136641825875337, "sync_wrapper", false, 16051944080121878777], [3129130049864710036, "memchr", false, 15247155475556316303], [5695049318159433696, "tower", false, 1778498415954398372], [7695812897323945497, "itoa", false, 2478430321954232087], [7712452662827335977, "tower_layer", false, 17633485529973314294], [7858942147296547339, "rustversion", false, 13787539941178566004], [8913795983780778928, "matchit", false, 10992404283847746246], [9010263965687315507, "http", false, 9985776661874788213], [9689903380558560274, "serde", false, 17554119073881279777], [10229185211513642314, "mime", false, 889530669170861422], [10629569228670356391, "futures_util", false, 7894425601274856401], [14084095096285906100, "http_body", false, 3650531633109126669], [15176407853393882315, "axum_core", false, 4328176883118545937], [16066129441945555748, "bytes", false, 376513669882553100], [16900715236047033623, "http_body_util", false, 4434650290111585496]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/axum-d614cf17af241c1a/dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}