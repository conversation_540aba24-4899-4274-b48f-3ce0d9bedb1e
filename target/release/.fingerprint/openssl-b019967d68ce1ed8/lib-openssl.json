{"rustc": 15497389221046826682, "features": "[\"default\", \"vendored\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"default\", \"unstable_boringssl\", \"v101\", \"v102\", \"v110\", \"v111\", \"vendored\"]", "target": 17474193825155910204, "profile": 5676177281124120482, "path": 2246932632676733693, "deps": [[2924422107542798392, "libc", false, 104084656086082668], [3722963349756955755, "once_cell", false, 17058549894504797386], [6635237767502169825, "foreign_types", false, 3206455933237101452], [7896293946984509699, "bitflags", false, 12658235077610237697], [8607891082156236373, "build_script_build", false, 9194084045484837741], [9070360545695802481, "ffi", false, 1197351337901435789], [10099563100786658307, "openssl_macros", false, 13514755976153147609], [10411997081178400487, "cfg_if", false, 3492306209465294775]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/openssl-b019967d68ce1ed8/dep-lib-openssl", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}