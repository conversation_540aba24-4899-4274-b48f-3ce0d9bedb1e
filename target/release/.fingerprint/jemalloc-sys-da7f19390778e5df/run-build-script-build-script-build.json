{"rustc": 15497389221046826682, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[3974924034656020041, "build_script_build", false, 8398384892094431693]], "local": [{"RerunIfChanged": {"output": "release/build/jemalloc-sys-da7f19390778e5df/output", "paths": ["<PERSON><PERSON><PERSON><PERSON>"]}}, {"RerunIfEnvChanged": {"var": "JEMALLOC_OVERRIDE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_ENABLE_DEBUG_OUTPUT", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "MACOSX_DEPLOYMENT_TARGET", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64_apple_darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_aarch64-apple-darwin", "val": null}}, {"RerunIfEnvChanged": {"var": "JEMALLOC_SYS_WITH_MALLOC_CONF", "val": null}}, {"RerunIfEnvChanged": {"var": "JEMALLOC_SYS_WITH_LG_PAGE", "val": null}}, {"RerunIfEnvChanged": {"var": "JEMALLOC_SYS_WITH_LG_HUGEPAGE", "val": null}}, {"RerunIfEnvChanged": {"var": "JEMALLOC_SYS_WITH_LG_QUANTUM", "val": null}}, {"RerunIfEnvChanged": {"var": "JEMALLOC_SYS_WITH_LG_VADDR", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}