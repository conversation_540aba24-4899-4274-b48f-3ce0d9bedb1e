/Users/<USER>/Projects/shreds-decoder/target/debug/deps/libshred_forwarder-bc78683745c2a8d4.rmeta: /Users/<USER>/Projects/shreds-decoder/clippy.toml src/lib.rs src/client/mod.rs src/client/erpc.rs src/config/mod.rs src/config/app.rs src/config/erpc.rs src/config/grpc.rs src/config/logging.rs src/config/performance.rs src/grpc/mod.rs src/grpc/broadcaster.rs src/grpc/server.rs src/grpc/service.rs src/types/mod.rs src/types/error.rs src/types/result.rs src/utils/mod.rs src/utils/cpu_affinity.rs src/utils/logging.rs src/utils/memory_pool.rs src/utils/metrics.rs src/utils/transaction.rs /Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-57b687015d12fbb7/out/shred_forwarder.rs Cargo.toml

/Users/<USER>/Projects/shreds-decoder/target/debug/deps/shred_forwarder-bc78683745c2a8d4.d: /Users/<USER>/Projects/shreds-decoder/clippy.toml src/lib.rs src/client/mod.rs src/client/erpc.rs src/config/mod.rs src/config/app.rs src/config/erpc.rs src/config/grpc.rs src/config/logging.rs src/config/performance.rs src/grpc/mod.rs src/grpc/broadcaster.rs src/grpc/server.rs src/grpc/service.rs src/types/mod.rs src/types/error.rs src/types/result.rs src/utils/mod.rs src/utils/cpu_affinity.rs src/utils/logging.rs src/utils/memory_pool.rs src/utils/metrics.rs src/utils/transaction.rs /Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-57b687015d12fbb7/out/shred_forwarder.rs Cargo.toml

/Users/<USER>/Projects/shreds-decoder/clippy.toml:
src/lib.rs:
src/client/mod.rs:
src/client/erpc.rs:
src/config/mod.rs:
src/config/app.rs:
src/config/erpc.rs:
src/config/grpc.rs:
src/config/logging.rs:
src/config/performance.rs:
src/grpc/mod.rs:
src/grpc/broadcaster.rs:
src/grpc/server.rs:
src/grpc/service.rs:
src/types/mod.rs:
src/types/error.rs:
src/types/result.rs:
src/utils/mod.rs:
src/utils/cpu_affinity.rs:
src/utils/logging.rs:
src/utils/memory_pool.rs:
src/utils/metrics.rs:
src/utils/transaction.rs:
/Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-57b687015d12fbb7/out/shred_forwarder.rs:
Cargo.toml:

# env-dep:CARGO_PKG_NAME=shred-forwarder
# env-dep:CARGO_PKG_VERSION=0.1.0
# env-dep:CLIPPY_ARGS=-D__CLIPPY_HACKERY__warnings__CLIPPY_HACKERY__
# env-dep:CLIPPY_CONF_DIR
# env-dep:OUT_DIR=/Users/<USER>/Projects/shreds-decoder/target/debug/build/shred-forwarder-57b687015d12fbb7/out
