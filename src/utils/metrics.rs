use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tracing::{debug, info};

pub struct AtomicMetrics {
    pub transactions_processed: AtomicU64,
    pub entries_processed: AtomicU64,
    pub grpc_connections: AtomicUsize,
    pub erpc_reconnections: AtomicU64,
    pub broadcast_errors: AtomicU64,
    pub deserialization_errors: AtomicU64,
    pub total_processing_time_nanos: AtomicU64,
    pub last_transaction_timestamp: AtomicU64,
    pub startup_time: AtomicU64,
}

impl AtomicMetrics {
    pub fn new() -> Self {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        Self {
            transactions_processed: AtomicU64::new(0),
            entries_processed: AtomicU64::new(0),
            grpc_connections: AtomicUsize::new(0),
            erpc_reconnections: AtomicU64::new(0),
            broadcast_errors: AtomicU64::new(0),
            deserialization_errors: AtomicU64::new(0),
            total_processing_time_nanos: AtomicU64::new(0),
            last_transaction_timestamp: AtomicU64::new(now),
            startup_time: AtomicU64::new(now),
        }
    }

    pub fn increment_transactions(&self) {
        self.transactions_processed.fetch_add(1, Ordering::Relaxed);
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        self.last_transaction_timestamp.store(now, Ordering::Relaxed);
    }

    pub fn increment_entries(&self) {
        self.entries_processed.fetch_add(1, Ordering::Relaxed);
    }

    pub fn increment_grpc_connections(&self) {
        self.grpc_connections.fetch_add(1, Ordering::Relaxed);
    }

    pub fn decrement_grpc_connections(&self) {
        self.grpc_connections.fetch_sub(1, Ordering::Relaxed);
    }

    pub fn increment_erpc_reconnections(&self) {
        self.erpc_reconnections.fetch_add(1, Ordering::Relaxed);
    }

    pub fn increment_broadcast_errors(&self) {
        self.broadcast_errors.fetch_add(1, Ordering::Relaxed);
    }

    pub fn increment_deserialization_errors(&self) {
        self.deserialization_errors.fetch_add(1, Ordering::Relaxed);
    }

    pub fn add_processing_time(&self, duration: Duration) {
        self.total_processing_time_nanos
            .fetch_add(duration.as_nanos() as u64, Ordering::Relaxed);
    }

    pub fn get_transactions_processed(&self) -> u64 {
        self.transactions_processed.load(Ordering::Relaxed)
    }

    pub fn get_entries_processed(&self) -> u64 {
        self.entries_processed.load(Ordering::Relaxed)
    }

    pub fn get_grpc_connections(&self) -> usize {
        self.grpc_connections.load(Ordering::Relaxed)
    }

    pub fn get_erpc_reconnections(&self) -> u64 {
        self.erpc_reconnections.load(Ordering::Relaxed)
    }

    pub fn get_broadcast_errors(&self) -> u64 {
        self.broadcast_errors.load(Ordering::Relaxed)
    }

    pub fn get_deserialization_errors(&self) -> u64 {
        self.deserialization_errors.load(Ordering::Relaxed)
    }

    pub fn get_average_processing_time_micros(&self) -> f64 {
        let total_nanos = self.total_processing_time_nanos.load(Ordering::Relaxed);
        let total_transactions = self.transactions_processed.load(Ordering::Relaxed);

        if total_transactions == 0 {
            0.0
        } else {
            (total_nanos as f64 / total_transactions as f64) / 1000.0
        }
    }

    pub fn get_uptime_seconds(&self) -> u64 {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        let startup = self.startup_time.load(Ordering::Relaxed);
        now.saturating_sub(startup)
    }

    pub fn get_transactions_per_second(&self) -> f64 {
        let uptime = self.get_uptime_seconds();
        if uptime == 0 {
            0.0
        } else {
            self.get_transactions_processed() as f64 / uptime as f64
        }
    }

    pub fn log_summary(&self) {
        info!(
            transactions_processed = self.get_transactions_processed(),
            entries_processed = self.get_entries_processed(),
            grpc_connections = self.get_grpc_connections(),
            erpc_reconnections = self.get_erpc_reconnections(),
            broadcast_errors = self.get_broadcast_errors(),
            deserialization_errors = self.get_deserialization_errors(),
            avg_processing_time_micros = %format!("{:.2}", self.get_average_processing_time_micros()),
            transactions_per_second = %format!("{:.2}", self.get_transactions_per_second()),
            uptime_seconds = self.get_uptime_seconds(),
            "Performance metrics summary"
        );
    }

    pub fn log_detailed(&self) {
        debug!(
            transactions_processed = self.get_transactions_processed(),
            entries_processed = self.get_entries_processed(),
            grpc_connections = self.get_grpc_connections(),
            erpc_reconnections = self.get_erpc_reconnections(),
            broadcast_errors = self.get_broadcast_errors(),
            deserialization_errors = self.get_deserialization_errors(),
            avg_processing_time_micros = %format!("{:.2}", self.get_average_processing_time_micros()),
            transactions_per_second = %format!("{:.2}", self.get_transactions_per_second()),
            uptime_seconds = self.get_uptime_seconds(),
            "Detailed performance metrics"
        );
    }
}

impl Default for AtomicMetrics {
    fn default() -> Self {
        Self::new()
    }
}

static GLOBAL_METRICS: std::sync::OnceLock<AtomicMetrics> = std::sync::OnceLock::new();

pub fn get_metrics() -> &'static AtomicMetrics {
    GLOBAL_METRICS.get_or_init(AtomicMetrics::new)
}

pub struct ProcessingTimer {
    start: Instant,
}

impl Default for ProcessingTimer {
    fn default() -> Self {
        Self::new()
    }
}

impl ProcessingTimer {
    pub fn new() -> Self {
        Self { start: Instant::now() }
    }
}

impl Drop for ProcessingTimer {
    fn drop(&mut self) {
        let duration = self.start.elapsed();
        get_metrics().add_processing_time(duration);
    }
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct MetricsConfig {
    pub enable_metrics: bool,
    pub enable_detailed_logging: bool,
    pub metrics_log_interval_seconds: u64,
    pub enable_processing_timers: bool,
}

impl Default for MetricsConfig {
    fn default() -> Self {
        Self {
            enable_metrics: true,
            enable_detailed_logging: false,
            metrics_log_interval_seconds: 300,
            enable_processing_timers: true,
        }
    }
}
