use crate::config::performance::CpuAffinityConfig;
use core_affinity::{CoreId, get_core_ids, set_for_current};
use tracing::{error, info, warn};

pub struct CpuAffinityManager {
    config: CpuAffinityConfig,
    available_cores: Vec<CoreId>,
}

impl CpuAffinityManager {
    pub fn new(config: CpuAffinityConfig) -> Self {
        let available_cores = get_core_ids().unwrap_or_default();

        info!(
            total_cores = available_cores.len(),
            enable_affinity = config.enable_cpu_affinity,
            "CPU affinity manager initialized"
        );

        Self {
            config,
            available_cores,
        }
    }

    pub fn set_erpc_client_affinity(&self) -> Result<(), String> {
        if !self.config.enable_cpu_affinity {
            return Ok(());
        }

        if let Some(core_id) = self.config.erpc_client_core_id {
            if core_id < self.available_cores.len() {
                let core = self.available_cores[core_id];
                if set_for_current(core) {
                    info!(core_id = core_id, "ERPC client pinned to CPU core");
                    Ok(())
                } else {
                    let msg = format!("Failed to pin ERPC client to core {}", core_id);
                    error!("{}", msg);
                    Err(msg)
                }
            } else {
                let msg = format!("Invalid core ID {} (max: {})", core_id, self.available_cores.len() - 1);
                error!("{}", msg);
                Err(msg)
            }
        } else {
            warn!("ERPC client core ID not specified");
            Ok(())
        }
    }

    pub fn set_grpc_server_affinity(&self) -> Result<(), String> {
        if !self.config.enable_cpu_affinity {
            return Ok(());
        }

        if !self.config.grpc_server_core_ids.is_empty() {
            let core_id = self.config.grpc_server_core_ids[0];

            if core_id < self.available_cores.len() {
                let core = self.available_cores[core_id];
                if set_for_current(core) {
                    info!(core_id = core_id, "gRPC server pinned to CPU core");
                    Ok(())
                } else {
                    let msg = format!("Failed to pin gRPC server to core {}", core_id);
                    error!("{}", msg);
                    Err(msg)
                }
            } else {
                let msg = format!("Invalid core ID {} (max: {})", core_id, self.available_cores.len() - 1);
                error!("{}", msg);
                Err(msg)
            }
        } else {
            warn!("gRPC server core IDs not specified");
            Ok(())
        }
    }
}

pub fn apply_cpu_affinity_for_erpc(config: &CpuAffinityConfig) -> Result<(), String> {
    let manager = CpuAffinityManager::new(config.clone());
    manager.set_erpc_client_affinity()
}

pub fn apply_cpu_affinity_for_grpc(config: &CpuAffinityConfig) -> Result<(), String> {
    let manager = CpuAffinityManager::new(config.clone());
    manager.set_grpc_server_affinity()
}
