use serde::{Deserialize, Serialize};

use crate::types::{ConfigError, ConfigResult};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GrpcConfig {
    pub bind_address: String,
    pub port: u16,
    pub broadcast_buffer_size: usize,
    pub tcp_nodelay: bool,
    pub tcp_keepalive_seconds: Option<u64>,
    pub http2_keepalive_interval_seconds: Option<u64>,
    pub http2_keepalive_timeout_seconds: Option<u64>,
}

impl Default for GrpcConfig {
    fn default() -> Self {
        Self {
            bind_address: "127.0.0.1".to_string(),
            port: 50051,
            broadcast_buffer_size: 10000,
            tcp_nodelay: true,
            tcp_keepalive_seconds: Some(30),
            http2_keepalive_interval_seconds: Some(30),
            http2_keepalive_timeout_seconds: Some(5),
        }
    }
}

impl GrpcConfig {
    pub fn validate(&self) -> ConfigResult<()> {
        if self.bind_address.is_empty() {
            return Err(ConfigError::InvalidValue {
                key: "grpc.bind_address".to_string(),
                value: self.bind_address.clone(),
            });
        }

        if self.port == 0 {
            return Err(ConfigError::InvalidValue {
                key: "grpc.port".to_string(),
                value: self.port.to_string(),
            });
        }

        Ok(())
    }
}
