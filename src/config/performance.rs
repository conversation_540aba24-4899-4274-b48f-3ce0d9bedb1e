use crate::utils::memory_pool::MemoryPoolConfig;
use crate::utils::metrics::MetricsConfig;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub enable_jemalloc: bool,
    pub memory_pools: MemoryPoolConfig,
    pub metrics: MetricsConfig,
    pub cpu_affinity: CpuAffinityConfig,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CpuAffinityConfig {
    pub enable_cpu_affinity: bool,
    pub erpc_client_core_id: Option<usize>,
    pub grpc_server_core_ids: Vec<usize>,
    pub auto_detect_cores: bool,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            enable_jemalloc: true,
            memory_pools: MemoryPoolConfig::default(),
            metrics: MetricsConfig::default(),
            cpu_affinity: CpuAffinityConfig::default(),
        }
    }
}

impl Default for CpuAffinityConfig {
    fn default() -> Self {
        Self {
            enable_cpu_affinity: false,
            erpc_client_core_id: None,
            grpc_server_core_ids: vec![],
            auto_detect_cores: true,
        }
    }
}
