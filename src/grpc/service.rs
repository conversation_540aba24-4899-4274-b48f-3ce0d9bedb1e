use std::pin::Pin;
use std::time::SystemTime;
use tokio::sync::broadcast;
use tokio_stream::{Stream, StreamExt, wrappers::BroadcastStream};
use tonic::{Request, Response, Status};
use tracing::info;

use super::proto::{
    PingRequest, PongResponse, SubscribeRequest, SubscribeResponse,
    shred_forwarder_service_server::ShredForwarderService,
};

use crate::config::GrpcConfig;
use crate::utils::get_metrics;

pub type SubscribeStream = Pin<Box<dyn Stream<Item = Result<SubscribeResponse, Status>> + Send>>;

#[derive(Clone)]
pub struct ShredForwarderServiceImpl {
    transaction_sender: broadcast::Sender<SubscribeResponse>,
}

impl ShredForwarderServiceImpl {
    pub fn new(config: GrpcConfig, _allowed_accounts: Vec<String>) -> Self {
        let (transaction_sender, _) = broadcast::channel(config.broadcast_buffer_size);

        Self { transaction_sender }
    }

    pub fn get_transaction_sender(&self) -> broadcast::Sender<SubscribeResponse> {
        self.transaction_sender.clone()
    }
}

#[tonic::async_trait]
impl ShredForwarderService for ShredForwarderServiceImpl {
    type SubscribeStream = SubscribeStream;

    async fn ping(&self, request: Request<PingRequest>) -> Result<Response<PongResponse>, Status> {
        let req = request.into_inner();

        info!(count = req.count, "Ping request received");

        let response = PongResponse {
            count: req.count,
            timestamp: Some(prost_types::Timestamp::from(SystemTime::now())),
            version: env!("CARGO_PKG_VERSION").to_string(),
        };

        Ok(Response::new(response))
    }

    async fn subscribe(&self, _request: Request<SubscribeRequest>) -> Result<Response<Self::SubscribeStream>, Status> {
        get_metrics().increment_grpc_connections();
        info!("New subscription request");

        let receiver = self.transaction_sender.subscribe();
        let stream = BroadcastStream::new(receiver).map(|result| match result {
            Ok(response) => Ok(response),
            Err(_) => {
                get_metrics().decrement_grpc_connections();
                Err(Status::internal("Stream error"))
            }
        });

        Ok(Response::new(Box::pin(stream)))
    }
}
